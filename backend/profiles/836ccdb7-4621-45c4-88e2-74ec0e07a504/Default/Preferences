{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "arm64", "shortcuts_version": 7}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1440, "left": 0, "maximized": false, "right": 2562, "top": 25, "work_area_bottom": 1440, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 25}, "window_placement_popup": {"bottom": 1226, "left": 1052, "maximized": false, "right": 1554, "top": 550, "work_area_bottom": 1440, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 25}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "549ce1e5-cbff-474a-b99f-0de833ef5bd1", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.23"}, "gaia_cookie": {"changed_time": **********.097512, "hash": "LHr2QP5Aa0k3pEGX5IMJvUDnAHs=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"Hd Yd\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-5u3oZULpki4/AAAAAAAAAAI/AAAAAAAAAAA/4jWzO032qqc/s48-c/photo.jpg\",1,1,0,null,1,\"117665609729486368698\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.macosx"}, "google": {"services": {"signin_scoped_device_id": "ebaf7b19-0221-42e6-9354-a4e1b3d0d794"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "13395894705152962", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "EVXcBLzdQwE+vHX1tdXHjaHH73XLuJ6V+MqyNNbVpv8Ule2ezFNj4xX9wHS3tVZjytM9suKN5ovg1KBP683ijw=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {"https://[*.]google.com,https://[*.]tiktok.com": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": 1}}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.tiktok.com:443,*": {"last_modified": "13395893994515884", "setting": {"https://www.tiktok.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "*****************"}}, "https://www.tiktok.com/foryou": {"couldShowBannerEvents": 1.3395893994515882e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com.vn:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]google.com.vn,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]tiktok.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com.vn:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.tiktok.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395894520170022e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.5, "rawScore": 4.5}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395894011573672e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 2.1}}, "https://www.tiktok.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395894717241214e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.***************, "rawScore": 7.***************}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.23", "creation_time": "13395893992181144", "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13395894717241214", "last_time_obsolete_http_credentials_removed": 1751421165.150735, "last_time_password_store_metrics_reported": 1751420422.21294, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chromium", "password_hash_data_list": [{"hash": "djEwN5GotkmitAzTyj7WXk/w0w==", "is_gaia": "djEwlYECCo/zlDoLthwIKSFM0A==", "last_signin": 1751420427.513219, "salt_length": "djEwoJKUl75QdAU/JLNWkH3dWXzu+ehq5zPj5gpHmguxMAg=", "username": "djEwKcDRscc8v/P/P9P1q4ZQc9PVTR6DhidjxdpSfFMJOkw="}], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13395893992", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ44Ss+env5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEP6ErPnp7+UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13395801599000000", "uma_in_sql_start_time": "13395893992214145"}, "sessions": {"event_log": [{"crashed": false, "time": "13395893992213366", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395894038929418", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395894062306851", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395894065242136", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395894504725880", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://www.tiktok.com/foryou": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}, "webauthn": {"touchid": {"metadata_secret": "bIx2VtooxOXwb0Svn1LSW0f9yc7TaIA0nLgiM/fyp2w="}}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"flow ai veo3\",\"chatgpt\",\"chuyển âm thành của video thành văn bản\",\"bigfoot\",\"ai credits needed per generation nghĩa là j\",\"notebooklm\",\"trae ai\",\"cách làm video bằng veo 3\"],[\"history\",\"history\",\"history\",\"history\",\"history\",\"history\",\"history\",\"history\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggestdetail\":[{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dflow+ai+veo3\\u0026deltok\\u003dAMc44K5UeCpE_F0GdazJHj08N6HZR0MJ_A\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dchatgpt\\u0026deltok\\u003dAMc44K5zrVxVvZ8sjhCGfkDp4bcMAc5tIA\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dchuy%E1%BB%83n+%C3%A2m+th%C3%A0nh+c%E1%BB%A7a+video+th%C3%A0nh+v%C4%83n+b%E1%BA%A3n\\u0026deltok\\u003dAMc44K5ZBZi46bZuI6lmvRiLwrMkOawXwg\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dbigfoot\\u0026deltok\\u003dAMc44K6cj6fB0CiM0l2Nnd1UrJabUxkhmA\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dai+credits+needed+per+generation+ngh%C4%A9a+l%C3%A0+j\\u0026deltok\\u003dAMc44K4BK9zSNTtjHXxGT82fA9Ix3X0OPw\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dnotebooklm\\u0026deltok\\u003dAMc44K73UvLK5K8YtLf_6QRFMf6tMgHX4w\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dtrae+ai\\u0026deltok\\u003dAMc44K55BjACv9HvjaZ_tq3OrDRYcmlvpw\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dc%C3%A1ch+l%C3%A0m+video+b%E1%BA%B1ng+veo+3\\u0026deltok\\u003dAMc44K4s9sDZ6BijNCtEj45Wbv0MzezLew\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000}],\"google:suggesteventid\":\"1318047871994390668\",\"google:suggestrelevance\":[607,606,605,604,603,602,601,600],\"google:suggestsubtypes\":[[362,39],[362,39],[362,39],[362,39],[362,39],[362,39],[362,39],[362,39]],\"google:suggesttype\":[\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\"],\"google:verbatimrelevance\":851}]"}}