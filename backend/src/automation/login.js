const { chromium } = require('playwright');
const path = require('path');
const { sleep } = require('../utils');
const AntidetectManager = require('../antidetect/antidetect-manager');
const HumanBehavior = require('../antidetect/human-behavior');

class TikTokLoginAutomation {
  constructor(wsServer, dbManager) {
    this.wsServer = wsServer;
    this.dbManager = dbManager;
    this.browsers = new Map(); // accountId -> browser instance
    this.antidetectManager = new AntidetectManager();
  }

  /**
   * Đăng nhập tài khoản TikTok
   */
  async loginAccount(accountId) {
    try {
      const account = await this.dbManager.getAccountById(accountId);
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      // Kiểm tra và đóng browser cũ nếu có
      await this.closeBrowser(accountId);

      this.wsServer.sendLog('info', `Starting login for ${account.username}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'logging_in' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'logging_in');

      // Tạo profile directory cho account
      const profileDir = path.join(__dirname, '../../profiles', accountId);

      // Lấy hoặc tạo persona cho account
      let persona = account.persona;
      if (!persona) {
        // Chọn persona dựa trên proxy location nếu có
        const preferredRegion = account.proxy ?
          this.antidetectManager.mapCountryToRegion(account.proxy.country) : null;
        persona = await this.antidetectManager.selectRandomPersona(preferredRegion);

        // Lưu persona vào account
        await this.dbManager.updateAccount(accountId, { persona: persona });
        this.wsServer.sendLog('info', `Assigned new persona ${persona.id} (${persona.platform}) to account ${account.username}`, accountId);
      } else {
        // Tái sử dụng persona đã có
        this.wsServer.sendLog('info', `Reusing existing persona ${persona.id} (${persona.platform}) for account ${account.username}`, accountId);

        // Kiểm tra xem persona có tồn tại trong ngân hàng không (có thể đã bị xóa)
        const existingPersona = await this.antidetectManager.getPersonaById(persona.id);
        if (!existingPersona) {
          this.wsServer.sendLog('warning', `Persona ${persona.id} not found in bank, selecting new one`, accountId);
          const preferredRegion = account.proxy ?
            this.antidetectManager.mapCountryToRegion(account.proxy.country) : null;
          persona = await this.antidetectManager.selectRandomPersona(preferredRegion);
          await this.dbManager.updateAccount(accountId, { persona: persona });
          this.wsServer.sendLog('info', `Assigned replacement persona ${persona.id} to account ${account.username}`, accountId);
        }
      }

      // Tạo context options với antidetect (sử dụng Google-specific nếu cần)
      const contextOptions = account.useGoogleLogin ?
        this.antidetectManager.createGoogleContextOptions(persona, account.proxy) :
        this.antidetectManager.createContextOptions(persona, account.proxy);
      const launchOptions = this.antidetectManager.createBrowserLaunchOptions();

      // Khởi tạo browser với profile và antidetect
      const browser = await chromium.launchPersistentContext(profileDir, {
        ...contextOptions,
        ...launchOptions,
        headless: false // Hiển thị browser để user có thể giải CAPTCHA
      });

      this.browsers.set(accountId, browser);

      // Inject spoofing script
      const spoofingScript = this.antidetectManager.createSpoofingScript(persona);
      await browser.addInitScript(spoofingScript);

      const page = await browser.newPage();

      // Initialize human behavior simulation
      const humanBehavior = new HumanBehavior(page);

      // Load cookies trước khi điều hướng
      await this.loadCookies(page, accountId);

      // Điều hướng đến trang đăng nhập TikTok
      this.wsServer.sendLog('info', `Navigating to TikTok login page`, accountId);

      if (account.useGoogleLogin) {
        await page.goto('https://www.tiktok.com/login', {
          waitUntil: 'networkidle'
        });
      } else {
        await page.goto('https://www.tiktok.com/login/phone-or-email/email', {
          waitUntil: 'networkidle'
        });
      }

      // Simulate human behavior - explore page briefly
      await humanBehavior.explorePage(2000);
      await humanBehavior.randomDelay(1000, 1000);

      // Kiểm tra xem đã đăng nhập chưa (sau khi load cookies)
      const isLoggedIn = await this.checkIfLoggedIn(page);
      if (isLoggedIn) {
        this.wsServer.sendLog('success', `Already logged in for account ${accountId}`, accountId);

        // Đóng browser vì đã đăng nhập sẵn
        await this.closeBrowser(accountId);

        await this.dbManager.updateAccount(accountId, { status: 'ready' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
        this.wsServer.sendLog('info', `Browser closed for account ${accountId}`, accountId);
        return true;
      }

      if (account.useGoogleLogin) {
        // Google OAuth login
        return await this.handleGoogleLogin(page, accountId, humanBehavior);
      } else {
        // Regular username/password login
        return await this.handleRegularLogin(page, account, accountId, humanBehavior);
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Login failed for account ${accountId}: ${error.message}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'error' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'error');

      // Cleanup browser on error
      await this.closeBrowser(accountId);
      return false;
    }
  }

  /**
   * Handle regular username/password login
   */
  async handleRegularLogin(page, account, accountId, humanBehavior) {
    try {
      // Điền thông tin đăng nhập
      this.wsServer.sendLog('info', `Filling login credentials`, accountId);

      // Use human-like form filling
      await humanBehavior.fillFormHumanLike([
        { selector: 'input[name="username"]', value: account.username },
        { selector: 'input[type="password"]', value: account.password }
      ]);

      // Simulate hesitation before clicking login
      await humanBehavior.simulateHesitation('submit');

      // Click nút đăng nhập with human behavior
      await humanBehavior.humanClick('button[data-e2e="login-button"]');

      this.wsServer.sendLog('info', `Login form submitted, waiting for response`, accountId);
      await sleep(3000, 5000);

      // Kiểm tra CAPTCHA hoặc lỗi đăng nhập
      const needsCaptcha = await this.checkForCaptcha(page);
      if (needsCaptcha) {
        this.wsServer.sendLog('warning', `CAPTCHA detected for ${account.username}. Please solve manually.`, accountId);
        await this.dbManager.updateAccount(accountId, { status: 'need_captcha' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'need_captcha');

        // Chờ user giải CAPTCHA (tối đa 5 phút)
        const captchaSolved = await this.waitForCaptchaSolution(page, accountId, 300000);
        if (!captchaSolved) {
          throw new Error('CAPTCHA not solved within timeout');
        }
      }

      // Kiểm tra đăng nhập thành công
      const loginSuccess = await this.waitForLoginSuccess(page, accountId, 30000);
      if (loginSuccess) {
        this.wsServer.sendLog('success', `Login successful for ${account.username}`, accountId);

        // Lưu cookies sau khi đăng nhập thành công
        await this.saveCookies(page, accountId);

        // Đóng browser sau khi đăng nhập thành công
        await this.closeBrowser(accountId);

        await this.dbManager.updateAccount(accountId, {
          status: 'ready',
          stats: {
            ...account.stats,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
        this.wsServer.sendLog('info', `Browser closed for account ${accountId}`, accountId);
        return true;
      } else {
        throw new Error('Login failed - unknown error');
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Regular login failed for account ${accountId}: ${error.message}`, accountId);
      throw error;
    }
  }

  /**
   * Handle Google OAuth login
   */
  async handleGoogleLogin(page, accountId, humanBehavior) {
    try {
      this.wsServer.sendLog('info', `Starting Google OAuth login`, accountId);

      // Simulate looking for Google button
      await humanBehavior.explorePage(1000);

      // Tìm và click nút đăng nhập bằng Google với human behavior
      const googleButtonSelectors = [
        '[data-e2e="google-login-button"]',
        'button:has-text("Continue with Google")',
        'button:has-text("Google")',
        'button[aria-label*="Google"]',
        '.google-login-button',
        'button:has([class*="google"])'
      ];

      let googleButton = null;
      for (const selector of googleButtonSelectors) {
        try {
          const button = page.locator(selector).first();
          if (await button.isVisible({ timeout: 2000 })) {
            googleButton = button;
            break;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      if (googleButton) {
        await humanBehavior.simulateHesitation('click');
        await googleButton.click();
        this.wsServer.sendLog('info', `Clicked Google login button, redirecting to TikTok login...`, accountId);
        await humanBehavior.randomDelay(3000, 2000);

        // Chờ redirect hoặc popup Google OAuth
        this.wsServer.sendLog('info', `Please complete Google OAuth login manually in the browser. You will be redirected to Google login page.`, accountId);

        // Đặt trạng thái là cần can thiệp thủ công
        await this.dbManager.updateAccount(accountId, { status: 'need_manual_login' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'need_manual_login');

        // Chờ đăng nhập thành công (timeout dài hơn cho Google OAuth)
        const loginSuccess = await this.waitForLoginSuccess(page, accountId, 180000); // 3 phút

        if (loginSuccess) {
          this.wsServer.sendLog('success', `Google OAuth login successful for account ${accountId}`, accountId);

          // Lưu cookies sau khi đăng nhập thành công
          await this.saveCookies(page, accountId);

          // Đóng browser sau khi đăng nhập thành công
          await this.closeBrowser(accountId);

          await this.dbManager.updateAccount(accountId, {
            status: 'ready',
            stats: {
              followsToday: 0,
              followsThisSession: 0,
              lastActivity: new Date().toISOString()
            }
          });
          this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
          this.wsServer.sendLog('info', `Browser closed for account ${accountId}`, accountId);
          return true;
        } else {
          throw new Error('Google OAuth login timeout - please try again');
        }
      } else {
        throw new Error('Google login button not found on the page');
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Google OAuth login failed for account ${accountId}: ${error.message}`, accountId);
      throw error;
    }
  }

  /**
   * Kiểm tra xem đã đăng nhập chưa
   */
  async checkIfLoggedIn(page) {
    try {
      // Kiểm tra các element chỉ xuất hiện khi đã đăng nhập
      const profileButton = page.locator('[data-e2e="profile-icon"]');
      const uploadButton = page.locator('[data-e2e="upload-icon"]');

      const isLoggedIn = await Promise.race([
        profileButton.isVisible().then(visible => visible),
        uploadButton.isVisible().then(visible => visible),
        sleep(5000).then(() => false)
      ]);

      return isLoggedIn;
    } catch (error) {
      return false;
    }
  }

  /**
   * Kiểm tra CAPTCHA
   */
  async checkForCaptcha(page) {
    try {
      const captchaSelectors = [
        '.captcha_verify_container',
        '[data-testid="captcha"]',
        '.verify-slider-track',
        '.secsdk-captcha-wrapper'
      ];

      for (const selector of captchaSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Chờ user giải CAPTCHA
   */
  async waitForCaptchaSolution(page, accountId, timeout = 300000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const hasCaptcha = await this.checkForCaptcha(page);
      if (!hasCaptcha) {
        this.wsServer.sendLog('success', `CAPTCHA solved for account`, accountId);
        return true;
      }

      await sleep(2000);
    }

    return false;
  }

  /**
   * Chờ đăng nhập thành công
   */
  async waitForLoginSuccess(page, accountId, timeout = 30000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const isLoggedIn = await this.checkIfLoggedIn(page);
      if (isLoggedIn) {
        return true;
      }

      // Kiểm tra lỗi đăng nhập
      const hasError = await this.checkForLoginError(page);
      if (hasError) {
        throw new Error('Invalid credentials or account locked');
      }

      await sleep(2000);
    }

    return false;
  }

  /**
   * Kiểm tra lỗi đăng nhập
   */
  async checkForLoginError(page) {
    try {
      const errorSelectors = [
        '.TUXTextError',
        '[data-e2e="login-error"]',
        '.error-message'
      ];

      for (const selector of errorSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Lưu cookies sau khi đăng nhập thành công
   */
  async saveCookies(page, accountId) {
    try {
      const cookies = await page.context().cookies();
      const cookiesPath = path.join(__dirname, '../../profiles', accountId, 'cookies.json');

      // Tạo thư mục nếu chưa có
      const fs = require('fs').promises;
      await fs.mkdir(path.dirname(cookiesPath), { recursive: true });

      // Lưu cookies
      await fs.writeFile(cookiesPath, JSON.stringify(cookies, null, 2));
      this.wsServer.sendLog('info', `Cookies saved for account ${accountId}`, accountId);
    } catch (error) {
      this.wsServer.sendLog('warning', `Failed to save cookies for account ${accountId}: ${error.message}`, accountId);
    }
  }

  /**
   * Load cookies cho account
   */
  async loadCookies(page, accountId) {
    try {
      const cookiesPath = path.join(__dirname, '../../profiles', accountId, 'cookies.json');
      const fs = require('fs').promises;

      const cookiesData = await fs.readFile(cookiesPath, 'utf8');
      const cookies = JSON.parse(cookiesData);

      await page.context().addCookies(cookies);
      this.wsServer.sendLog('info', `Cookies loaded for account ${accountId}`, accountId);
      return true;
    } catch (error) {
      this.wsServer.sendLog('info', `No existing cookies found for account ${accountId}`, accountId);
      return false;
    }
  }

  /**
   * Đóng browser cho account
   */
  async closeBrowser(accountId) {
    const browser = this.browsers.get(accountId);
    if (browser) {
      try {
        await browser.close();
      } catch (error) {
        console.error(`Error closing browser for account ${accountId}:`, error);
      } finally {
        this.browsers.delete(accountId);
      }
    }
  }

  /**
   * Lấy browser instance cho account
   */
  getBrowser(accountId) {
    return this.browsers.get(accountId);
  }

  /**
   * Đóng tất cả browsers
   */
  async closeAllBrowsers() {
    for (const [accountId, browser] of this.browsers) {
      try {
        await browser.close();
      } catch (error) {
        console.error(`Error closing browser for ${accountId}:`, error);
      }
    }
    this.browsers.clear();
  }
}

module.exports = TikTokLoginAutomation;
