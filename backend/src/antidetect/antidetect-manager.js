/**
 * Antidetect Manager
 * <PERSON>u<PERSON>n lý việc áp dụng personas vào browser contexts
 */

const path = require('path');
const fs = require('fs').promises;
const PersonaGenerator = require('./persona-generator');
const { TIMEZONE_BY_REGION, LANGUAGE_BY_REGION } = require('./fingerprint-data');

class AntidetectManager {
  constructor() {
    this.personas = [];
    this.personasLoaded = false;
    this.personaGenerator = new PersonaGenerator();
  }

  /**
   * Load personas từ file
   * @returns {Promise<void>}
   */
  async loadPersonas() {
    if (this.personasLoaded) return;

    try {
      const personasPath = path.join(__dirname, '../../data/personas.json');
      console.log(`Loading personas from: ${personasPath}`);
      const content = await fs.readFile(personasPath, 'utf-8');
      const data = JSON.parse(content);

      // Check if data is array directly or wrapped in object
      if (Array.isArray(data)) {
        this.personas = data;
      } else if (data.personas && Array.isArray(data.personas)) {
        this.personas = data.personas;
      } else {
        throw new Error('Invalid personas data format');
      }

      this.personasLoaded = true;
      console.log(`✅ Successfully loaded ${this.personas.length} personas`);

      // Log first persona for verification
      if (this.personas.length > 0) {
        console.log(`First persona: ${this.personas[0].id} (${this.personas[0].platform})`);
      }
    } catch (error) {
      console.error('❌ Error loading personas:', error.message);
      // Fallback: tạo một số personas mặc định
      this.personas = [
        this.personaGenerator.generateWindowsPersona('US'),
        this.personaGenerator.generateMacPersona('US'),
        this.personaGenerator.generateWindowsPersona('GB'),
        this.personaGenerator.generateMacPersona('CA')
      ];
      this.personasLoaded = true;
      console.log(`⚠️  Using ${this.personas.length} fallback personas`);
    }
  }

  /**
   * Lựa chọn persona ngẫu nhiên
   * @param {string} preferredRegion - Region ưu tiên (optional)
   * @returns {Object} - Persona object
   */
  async selectRandomPersona(preferredRegion = null) {
    await this.loadPersonas();

    console.log(`🎭 Selecting persona from ${this.personas.length} available personas`);
    if (preferredRegion) {
      console.log(`🌍 Preferred region: ${preferredRegion}`);
    }

    let availablePersonas = this.personas;

    // Nếu có region ưu tiên, ưu tiên chọn persona từ region đó
    if (preferredRegion) {
      const regionPersonas = this.personas.filter(p => p.region === preferredRegion);
      console.log(`Found ${regionPersonas.length} personas for region ${preferredRegion}`);
      if (regionPersonas.length > 0) {
        availablePersonas = regionPersonas;
      } else {
        console.log(`⚠️  No personas found for region ${preferredRegion}, using all available personas`);
      }
    }

    if (availablePersonas.length === 0) {
      console.error('❌ No personas available after filtering');
      console.error(`Total personas loaded: ${this.personas.length}`);
      console.error(`Personas loaded status: ${this.personasLoaded}`);
      throw new Error(`No personas available. Total loaded: ${this.personas.length}, Preferred region: ${preferredRegion || 'any'}`);
    }

    const randomIndex = Math.floor(Math.random() * availablePersonas.length);
    const selectedPersona = availablePersonas[randomIndex];
    console.log(`✅ Selected persona: ${selectedPersona.id} (${selectedPersona.platform}, ${selectedPersona.region})`);
    return selectedPersona;
  }

  /**
   * Ánh xạ từ proxy location đến region code
   * @param {string} country - Mã quốc gia từ proxy
   * @returns {string} - Region code
   */
  mapCountryToRegion(country) {
    const mapping = {
      'US': 'US',
      'CA': 'CA', 
      'GB': 'GB',
      'UK': 'GB',
      'DE': 'DE',
      'FR': 'FR',
      'IT': 'IT',
      'ES': 'ES',
      'AU': 'AU',
      'JP': 'JP',
      'KR': 'KR',
      'CN': 'CN',
      'IN': 'IN',
      'BR': 'BR',
      'MX': 'MX'
    };

    return mapping[country?.toUpperCase()] || 'US';
  }

  /**
   * Tạo timezone và locale từ proxy location với enhanced consistency
   * @param {Object} proxy - Proxy object với country và city
   * @returns {Object} - { timezone, locale, languages }
   */
  generateLocaleFromProxy(proxy) {
    if (!proxy || !proxy.country) {
      return {
        timezone: 'America/New_York',
        locale: 'en-US',
        languages: ['en-US', 'en'],
        country: 'US',
        region: 'US'
      };
    }

    const region = this.mapCountryToRegion(proxy.country);
    const timezones = TIMEZONE_BY_REGION[region] || TIMEZONE_BY_REGION['US'];
    const language = LANGUAGE_BY_REGION[region] || LANGUAGE_BY_REGION['US'];

    // Enhanced timezone selection based on city if available
    let timezone;
    if (proxy.city) {
      timezone = this.getCityTimezone(proxy.city, proxy.country);
    }
    if (!timezone) {
      timezone = timezones[Math.floor(Math.random() * timezones.length)];
    }

    return {
      timezone,
      locale: language.primary,
      languages: [language.primary, ...language.secondary],
      country: proxy.country,
      region
    };
  }

  /**
   * Get timezone for specific city
   * @param {string} city - City name
   * @param {string} country - Country code
   * @returns {string|null} - Timezone or null if not found
   */
  getCityTimezone(city, country) {
    const cityTimezones = {
      'US': {
        'New York': 'America/New_York',
        'Los Angeles': 'America/Los_Angeles',
        'Chicago': 'America/Chicago',
        'Houston': 'America/Chicago',
        'Phoenix': 'America/Phoenix',
        'Philadelphia': 'America/New_York',
        'San Antonio': 'America/Chicago',
        'San Diego': 'America/Los_Angeles',
        'Dallas': 'America/Chicago',
        'San Jose': 'America/Los_Angeles',
        'Austin': 'America/Chicago',
        'Seattle': 'America/Los_Angeles',
        'Denver': 'America/Denver',
        'Washington': 'America/New_York',
        'Boston': 'America/New_York',
        'Miami': 'America/New_York',
        'Atlanta': 'America/New_York',
        'Las Vegas': 'America/Los_Angeles',
        'Portland': 'America/Los_Angeles',
        'San Francisco': 'America/Los_Angeles'
      },
      'GB': {
        'London': 'Europe/London',
        'Birmingham': 'Europe/London',
        'Manchester': 'Europe/London',
        'Glasgow': 'Europe/London',
        'Liverpool': 'Europe/London',
        'Leeds': 'Europe/London',
        'Edinburgh': 'Europe/London',
        'Bristol': 'Europe/London',
        'Cardiff': 'Europe/London'
      },
      'DE': {
        'Berlin': 'Europe/Berlin',
        'Hamburg': 'Europe/Berlin',
        'Munich': 'Europe/Berlin',
        'Cologne': 'Europe/Berlin',
        'Frankfurt': 'Europe/Berlin',
        'Stuttgart': 'Europe/Berlin'
      },
      'FR': {
        'Paris': 'Europe/Paris',
        'Marseille': 'Europe/Paris',
        'Lyon': 'Europe/Paris',
        'Toulouse': 'Europe/Paris',
        'Nice': 'Europe/Paris',
        'Bordeaux': 'Europe/Paris'
      },
      'CA': {
        'Toronto': 'America/Toronto',
        'Montreal': 'America/Montreal',
        'Vancouver': 'America/Vancouver',
        'Calgary': 'America/Edmonton',
        'Edmonton': 'America/Edmonton',
        'Ottawa': 'America/Toronto'
      },
      'AU': {
        'Sydney': 'Australia/Sydney',
        'Melbourne': 'Australia/Melbourne',
        'Brisbane': 'Australia/Brisbane',
        'Perth': 'Australia/Perth',
        'Adelaide': 'Australia/Adelaide'
      }
    };

    const countryTimezones = cityTimezones[country];
    if (!countryTimezones) return null;

    // Try exact match first
    if (countryTimezones[city]) {
      return countryTimezones[city];
    }

    // Try case-insensitive match
    const cityLower = city.toLowerCase();
    for (const [cityName, timezone] of Object.entries(countryTimezones)) {
      if (cityName.toLowerCase() === cityLower) {
        return timezone;
      }
    }

    // Try partial match
    for (const [cityName, timezone] of Object.entries(countryTimezones)) {
      if (cityName.toLowerCase().includes(cityLower) || cityLower.includes(cityName.toLowerCase())) {
        return timezone;
      }
    }

    return null;
  }

  /**
   * Tạo geolocation từ proxy
   * @param {Object} proxy - Proxy object
   * @returns {Object} - Geolocation object
   */
  generateGeolocationFromProxy(proxy) {
    if (!proxy || !proxy.country) {
      return {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 100
      };
    }

    // Sử dụng persona generator để tạo geolocation
    const region = this.mapCountryToRegion(proxy.country);
    return this.personaGenerator.generateGeolocation(region);
  }

  /**
   * Tạo browser context options từ persona và proxy
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @returns {Object} - Playwright context options
   */
  createContextOptions(persona, proxy = null) {
    const localeInfo = proxy ? this.generateLocaleFromProxy(proxy) : {
      timezone: persona.timezone,
      locale: persona.language,
      languages: persona.languages
    };

    const geolocation = proxy ? this.generateGeolocationFromProxy(proxy) : persona.geolocation;

    const options = {
      userAgent: persona.userAgent,
      viewport: {
        width: persona.screen.width,
        height: persona.screen.height
      },
      screen: {
        width: persona.screen.width,
        height: persona.screen.height
      },
      locale: localeInfo.locale,
      timezoneId: localeInfo.timezone,
      geolocation: {
        latitude: geolocation.latitude,
        longitude: geolocation.longitude,
        accuracy: geolocation.accuracy
      },
      permissions: ['geolocation'],
      deviceScaleFactor: 1,
      isMobile: false,
      hasTouch: false,
      colorScheme: 'light',
      reducedMotion: 'no-preference',
      forcedColors: 'none',
      extraHTTPHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': localeInfo.languages.map((lang, index) => {
          const quality = index === 0 ? '' : `;q=${(0.9 - index * 0.1).toFixed(1)}`;
          return `${lang}${quality}`;
        }).join(','),
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': `"Not_A Brand";v="8", "Chromium";v="${this.extractChromeVersion(persona.userAgent)}", "Google Chrome";v="${this.extractChromeVersion(persona.userAgent)}"`,
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': `"${persona.platform === 'Windows' ? 'Windows' : 'macOS'}"`,
        'DNT': '1'
      }
    };

    // Thêm proxy nếu có
    if (proxy && proxy.type !== 'No proxy') {
      try {
        console.log(`🌐 Configuring proxy: ${proxy.type} ${proxy.host}:${proxy.port}`);
        const ProxyService = require('../services/ProxyService');
        const proxyService = new ProxyService();

        // Kiểm tra loại proxy và xử lý phù hợp
        const proxyType = proxy.type.toLowerCase();

        if (proxyType === 'socks5' && proxy.username && proxy.password) {
          // SOCKS5 với auth: Playwright không hỗ trợ trực tiếp
          console.log(`⚠️  SOCKS5 with auth detected - trying alternative approach`);

          // Thử sử dụng HTTP proxy thay thế nếu có thông tin
          if (proxy.httpFallback) {
            console.log(`   Using HTTP fallback proxy`);
            options.proxy = proxyService.createProxyConfig(proxy.httpFallback);
          } else {
            console.log(`   Warning: SOCKS5 auth not supported by browser, using direct connection`);
            console.log(`   Consider using HTTP/HTTPS proxy for better compatibility`);
          }
        } else {
          // Các loại proxy khác (HTTP, HTTPS, SOCKS5 không auth)
          options.proxy = proxyService.createProxyConfig(proxy);
          console.log(`✅ Proxy configured: ${options.proxy.server}`);

          // Thêm thông tin auth nếu có
          if (options.proxy.username) {
            console.log(`   Authentication: ${options.proxy.username}:***`);
          }
        }
      } catch (error) {
        console.error(`❌ Error configuring proxy: ${error.message}`);

        // Xử lý lỗi cụ thể
        if (error.message.includes('socks5 proxy authentication')) {
          console.log(`   SOCKS5 auth not supported - continuing without proxy`);
          console.log(`   Recommendation: Use HTTP/HTTPS proxy instead`);
        } else if (error.message.includes('Invalid proxy configuration')) {
          console.log(`   Invalid proxy config - check host/port/credentials`);
          throw error;
        } else {
          console.log(`   Unexpected proxy error - continuing without proxy`);
        }
      }
    } else {
      console.log(`🌐 No proxy configured - using direct connection`);
    }

    return options;
  }

  /**
   * Tạo browser launch options với antidetect
   * @returns {Object} - Browser launch options
   */
  createBrowserLaunchOptions() {
    return {
      args: [
        // Core antidetection flags
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor',
        '--disable-features=WebRtcHideLocalIpsWithMdns',
        '--disable-features=TranslateUI',
        '--disable-features=ScriptStreaming',
        '--disable-features=V8OptimizeJavascript',
        '--disable-features=VizServiceSharedBitmapManager',

        // Disable automation detection
        '--exclude-switches=enable-automation',
        '--disable-automation',
        '--disable-blink-features=AutomationControlled',
        '--disable-infobars',
        '--disable-dev-shm-usage',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-plugins-discovery',

        // Disable webdriver detection
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-background-timer-throttling',
        '--disable-background-networking',

        // Disable tracking and telemetry
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--disable-hang-monitor',
        '--disable-popup-blocking',
        '--disable-prompt-on-repost',
        '--disable-sync',
        '--disable-domain-reliability',
        '--disable-component-update',
        '--disable-background-downloads',
        '--disable-add-to-shelf',
        '--disable-breakpad',

        // Performance and stability
        '--no-sandbox',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-pings',
        '--no-zygote',
        '--metrics-recording-only',
        '--password-store=basic',
        '--use-mock-keychain',
        '--disable-gpu-sandbox',
        '--disable-software-rasterizer',
        '--disable-gpu-watchdog',

        // Additional stealth flags
        '--disable-features=UserAgentClientHint',
        '--disable-features=WebPayments',
        '--disable-features=WebUSB',
        '--disable-features=WebBluetooth',
        '--disable-features=MediaRouter',
        '--disable-features=DialMediaRouteProvider',
        '--disable-features=CastMediaRouteProvider',
        '--disable-logging',
        '--silent',
        '--disable-gpu-process-crash-limit'
      ]
    };
  }

  /**
   * Extract Chrome version from User-Agent
   * @param {string} userAgent - User-Agent string
   * @returns {string} - Chrome version
   */
  extractChromeVersion(userAgent) {
    const match = userAgent.match(/Chrome\/(\d+)/);
    return match ? match[1] : '122';
  }

  /**
   * Lấy persona theo ID
   * @param {string} personaId - ID của persona
   * @returns {Object|null} - Persona object hoặc null
   */
  async getPersonaById(personaId) {
    await this.loadPersonas();
    return this.personas.find(p => p.id === personaId) || null;
  }

  /**
   * Tạo script spoofing cho persona
   * @param {Object} persona - Persona object
   * @returns {string} - JavaScript code để inject
   */
  createSpoofingScript(persona) {
    return `
      // Remove webdriver property
      (function() {
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });

        // Remove automation flags
        delete navigator.__webdriver_script_fn;
        delete navigator.__webdriver_evaluate;
        delete navigator.__webdriver_unwrapped;
        delete navigator.__fxdriver_evaluate;
        delete navigator.__fxdriver_unwrapped;
        delete navigator.__driver_evaluate;
        delete navigator.__webdriver_evaluate;
        delete navigator.__selenium_evaluate;
        delete navigator.__selenium_unwrapped;
        delete navigator.__driver_unwrapped;
      })();

      // Chrome runtime spoofing
      (function() {
        if (window.chrome && window.chrome.runtime) {
          Object.defineProperty(window.chrome.runtime, 'onConnect', {
            value: undefined,
            configurable: true
          });
          Object.defineProperty(window.chrome.runtime, 'onMessage', {
            value: undefined,
            configurable: true
          });
        }

        // Add realistic chrome object if missing
        if (!window.chrome) {
          window.chrome = {
            runtime: {
              onConnect: undefined,
              onMessage: undefined
            }
          };
        }
      })();

      // Navigator properties spoofing
      (function() {
        // Platform spoofing
        Object.defineProperty(navigator, 'platform', {
          get: () => '${persona.platform === 'Windows' ? 'Win32' : 'MacIntel'}',
          configurable: true
        });

        // Languages spoofing
        Object.defineProperty(navigator, 'languages', {
          get: () => ${JSON.stringify(persona.languages)},
          configurable: true
        });

        // Hardware concurrency
        Object.defineProperty(navigator, 'hardwareConcurrency', {
          get: () => ${persona.hardware.hardwareConcurrency},
          configurable: true
        });

        // Device memory
        Object.defineProperty(navigator, 'deviceMemory', {
          get: () => ${persona.hardware.deviceMemory},
          configurable: true
        });

        // User agent
        Object.defineProperty(navigator, 'userAgent', {
          get: () => '${persona.userAgent}',
          configurable: true
        });

        // App version
        Object.defineProperty(navigator, 'appVersion', {
          get: () => '${persona.userAgent.substring(persona.userAgent.indexOf('/') + 1)}',
          configurable: true
        });
      })();

      // WebGL Spoofing (Enhanced with more parameters)
      (function() {
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
          if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
            return '${persona.webgl.vendor}';
          }
          if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
            return '${persona.webgl.renderer}';
          }
          if (parameter === 7936) { // VERSION
            return '${persona.webgl.version}';
          }
          if (parameter === 35724) { // SHADING_LANGUAGE_VERSION
            return '${persona.webgl.shadingLanguageVersion}';
          }
          if (parameter === 3379) { // MAX_TEXTURE_SIZE
            return ${persona.webgl.maxTextureSize || 16384};
          }
          if (parameter === 34921) { // MAX_VERTEX_ATTRIBS
            return ${persona.webgl.maxVertexAttribs || 16};
          }
          if (parameter === 36348) { // MAX_VARYING_VECTORS
            return ${persona.webgl.maxVaryingVectors || 30};
          }
          if (parameter === 33901) { // ALIASED_LINE_WIDTH_RANGE
            return new Float32Array([${persona.webgl.aliasedLineWidthRange ? persona.webgl.aliasedLineWidthRange.join(',') : '1,1'}]);
          }
          if (parameter === 33902) { // ALIASED_POINT_SIZE_RANGE
            return new Float32Array([${persona.webgl.aliasedPointSizeRange ? persona.webgl.aliasedPointSizeRange.join(',') : '1,1024'}]);
          }
          return getParameter.call(this, parameter);
        };

        if (window.WebGL2RenderingContext) {
          const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
          WebGL2RenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) {
              return '${persona.webgl.vendor}';
            }
            if (parameter === 37446) {
              return '${persona.webgl.renderer}';
            }
            if (parameter === 7936) {
              return '${persona.webgl.version}';
            }
            if (parameter === 35724) {
              return '${persona.webgl.shadingLanguageVersion}';
            }
            if (parameter === 3379) {
              return ${persona.webgl.maxTextureSize || 16384};
            }
            if (parameter === 34921) {
              return ${persona.webgl.maxVertexAttribs || 16};
            }
            if (parameter === 36348) {
              return ${persona.webgl.maxVaryingVectors || 30};
            }
            if (parameter === 33901) {
              return new Float32Array([${persona.webgl.aliasedLineWidthRange ? persona.webgl.aliasedLineWidthRange.join(',') : '1,1'}]);
            }
            if (parameter === 33902) {
              return new Float32Array([${persona.webgl.aliasedPointSizeRange ? persona.webgl.aliasedPointSizeRange.join(',') : '1,1024'}]);
            }
            return getParameter2.call(this, parameter);
          };
        }

        // Spoof WebGL extensions
        const getSupportedExtensions = WebGLRenderingContext.prototype.getSupportedExtensions;
        WebGLRenderingContext.prototype.getSupportedExtensions = function() {
          const extensions = getSupportedExtensions.call(this);
          // Add realistic extensions based on GPU vendor
          const vendorExtensions = '${persona.webgl.vendor}'.includes('NVIDIA') ? [
            'WEBGL_debug_renderer_info',
            'WEBGL_debug_shaders',
            'WEBGL_lose_context',
            'OES_element_index_uint',
            'OES_standard_derivatives',
            'OES_vertex_array_object',
            'WEBGL_compressed_texture_s3tc'
          ] : [
            'WEBGL_debug_renderer_info',
            'WEBGL_debug_shaders',
            'WEBGL_lose_context',
            'OES_element_index_uint',
            'OES_standard_derivatives',
            'OES_vertex_array_object'
          ];
          return [...new Set([...extensions, ...vendorExtensions])];
        };
      })();

      // Canvas Fingerprinting Protection (Enhanced)
      (function() {
        const toDataURL = HTMLCanvasElement.prototype.toDataURL;
        const getImageData = CanvasRenderingContext2D.prototype.getImageData;

        HTMLCanvasElement.prototype.toDataURL = function() {
          const result = toDataURL.apply(this, arguments);
          // Add subtle noise based on persona
          const noise = Math.sin(${persona.canvas.noiseLevel} * Math.PI) * 0.0001;
          return result.replace(/data:image\\/png;base64,/, 'data:image/png;base64,' + btoa(String.fromCharCode(Math.floor(noise * 255))).slice(0, 4));
        };

        CanvasRenderingContext2D.prototype.getImageData = function() {
          const result = getImageData.apply(this, arguments);
          // Add minimal noise to image data
          const data = result.data;
          for (let i = 0; i < data.length; i += 4) {
            if (Math.random() < ${persona.canvas.noiseLevel}) {
              data[i] = Math.min(255, data[i] + Math.floor(Math.random() * 3) - 1);
              data[i + 1] = Math.min(255, data[i + 1] + Math.floor(Math.random() * 3) - 1);
              data[i + 2] = Math.min(255, data[i + 2] + Math.floor(Math.random() * 3) - 1);
            }
          }
          return result;
        };
      })();

      // Screen properties spoofing
      (function() {
        Object.defineProperty(screen, 'width', {
          get: () => ${persona.screen.width},
          configurable: true
        });
        Object.defineProperty(screen, 'height', {
          get: () => ${persona.screen.height},
          configurable: true
        });
        Object.defineProperty(screen, 'availWidth', {
          get: () => ${persona.screen.availWidth},
          configurable: true
        });
        Object.defineProperty(screen, 'availHeight', {
          get: () => ${persona.screen.availHeight},
          configurable: true
        });
        Object.defineProperty(screen, 'colorDepth', {
          get: () => ${persona.screen.colorDepth},
          configurable: true
        });
        Object.defineProperty(screen, 'pixelDepth', {
          get: () => ${persona.screen.pixelDepth},
          configurable: true
        });
      })();

      // Plugins and mimeTypes spoofing
      (function() {
        const plugins = [
          { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
          { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
          { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
        ];

        Object.defineProperty(navigator, 'plugins', {
          get: () => plugins,
          configurable: true
        });

        const mimeTypes = [
          { type: 'application/pdf', suffixes: 'pdf', description: 'Portable Document Format', enabledPlugin: plugins[0] },
          { type: 'text/pdf', suffixes: 'pdf', description: 'Portable Document Format', enabledPlugin: plugins[0] }
        ];

        Object.defineProperty(navigator, 'mimeTypes', {
          get: () => mimeTypes,
          configurable: true
        });
      })();

      // AudioContext fingerprinting protection
      (function() {
        if (window.AudioContext || window.webkitAudioContext) {
          const AudioContext = window.AudioContext || window.webkitAudioContext;
          const originalCreateAnalyser = AudioContext.prototype.createAnalyser;

          AudioContext.prototype.createAnalyser = function() {
            const analyser = originalCreateAnalyser.call(this);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;

            analyser.getFloatFrequencyData = function(array) {
              originalGetFloatFrequencyData.call(this, array);
              // Add minimal noise to audio fingerprint
              for (let i = 0; i < array.length; i++) {
                array[i] += (Math.random() - 0.5) * 0.0001;
              }
            };

            return analyser;
          };
        }
      })();

      // Permissions API spoofing
      (function() {
        if (navigator.permissions && navigator.permissions.query) {
          const originalQuery = navigator.permissions.query;
          navigator.permissions.query = function(permissionDesc) {
            return originalQuery.call(this, permissionDesc).then(result => {
              // Modify certain permissions to appear more natural
              if (permissionDesc.name === 'notifications') {
                Object.defineProperty(result, 'state', { value: 'default', configurable: true });
              }
              return result;
            });
          };
        }
      })();

      // Battery API removal (privacy concern)
      (function() {
        if (navigator.getBattery) {
          Object.defineProperty(navigator, 'getBattery', {
            get: () => undefined,
            configurable: true
          });
        }
        delete navigator.battery;
      })();

      // Connection API spoofing
      (function() {
        if (navigator.connection) {
          Object.defineProperty(navigator.connection, 'effectiveType', {
            get: () => '${persona.network ? persona.network.effectiveType : '4g'}',
            configurable: true
          });
          Object.defineProperty(navigator.connection, 'downlink', {
            get: () => ${persona.network ? persona.network.downlink : 10},
            configurable: true
          });
          Object.defineProperty(navigator.connection, 'rtt', {
            get: () => ${persona.network ? persona.network.rtt : 50},
            configurable: true
          });
        }
      })();

      // Timezone spoofing
      (function() {
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {
          // Calculate offset based on persona timezone
          return originalGetTimezoneOffset.call(this);
        };

        // Spoof Intl.DateTimeFormat
        if (window.Intl && window.Intl.DateTimeFormat) {
          const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
          Intl.DateTimeFormat.prototype.resolvedOptions = function() {
            const options = originalResolvedOptions.call(this);
            options.timeZone = '${persona.timezone}';
            return options;
          };
        }
      })();

      // Remove automation indicators
      (function() {
        // Remove common automation detection properties
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;

        // Remove Selenium indicators
        delete window._selenium;
        delete window.__selenium_evaluate;
        delete window.__selenium_unwrapped;
        delete window.__webdriver_script_fn;
        delete window.__webdriver_evaluate;
        delete window.__webdriver_unwrapped;
        delete window.__fxdriver_evaluate;
        delete window.__fxdriver_unwrapped;
        delete window.__driver_evaluate;
        delete window.__driver_unwrapped;

        // Remove PhantomJS indicators
        delete window.callPhantom;
        delete window._phantom;
        delete window.__phantom;

        // Remove Nightmare indicators
        delete window.__nightmare;
        delete window.nightmare;
      })();

      // Google-specific evasion techniques
      (function() {
        // Spoof Google's bot detection checks
        if (window.google && window.google.accounts) {
          // Make Google accounts API appear more natural
          const originalInit = window.google.accounts.id.initialize;
          if (originalInit) {
            window.google.accounts.id.initialize = function(config) {
              // Add delay to make it seem more human
              setTimeout(() => originalInit.call(this, config), Math.random() * 100 + 50);
            };
          }
        }

        // Spoof reCAPTCHA detection
        if (window.grecaptcha) {
          const originalRender = window.grecaptcha.render;
          if (originalRender) {
            window.grecaptcha.render = function() {
              // Add human-like delay
              setTimeout(() => originalRender.apply(this, arguments), Math.random() * 200 + 100);
            };
          }
        }

        // Override iframe detection
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
          const element = originalCreateElement.call(this, tagName);
          if (tagName.toLowerCase() === 'iframe') {
            // Add realistic iframe properties
            Object.defineProperty(element, 'contentWindow', {
              get: function() {
                return {
                  location: { href: 'about:blank' },
                  document: { readyState: 'complete' }
                };
              }
            });
          }
          return element;
        };

        // Spoof Google Analytics detection
        if (window.gtag) {
          const originalGtag = window.gtag;
          window.gtag = function() {
            // Add delay to analytics calls
            setTimeout(() => originalGtag.apply(this, arguments), Math.random() * 50 + 25);
          };
        }

        // Override fetch for Google APIs
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
          // Add realistic delays for Google API calls
          if (typeof url === 'string' && (url.includes('google') || url.includes('gstatic'))) {
            return new Promise(resolve => {
              setTimeout(() => {
                resolve(originalFetch.call(this, url, options));
              }, Math.random() * 100 + 50);
            });
          }
          return originalFetch.call(this, url, options);
        };

        // Spoof mouse and keyboard events to appear more human
        const addHumanBehavior = () => {
          // Add subtle mouse movements
          let mouseX = 0, mouseY = 0;
          document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
          });

          // Simulate natural mouse movements
          setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance every interval
              const event = new MouseEvent('mousemove', {
                clientX: mouseX + (Math.random() - 0.5) * 2,
                clientY: mouseY + (Math.random() - 0.5) * 2,
                bubbles: true
              });
              document.dispatchEvent(event);
            }
          }, 1000 + Math.random() * 2000);
        };

        // Initialize human behavior simulation
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', addHumanBehavior);
        } else {
          addHumanBehavior();
        }

        // Override performance.now() to add realistic timing
        const originalPerformanceNow = performance.now;
        performance.now = function() {
          return originalPerformanceNow.call(this) + Math.random() * 0.1;
        };

        // Spoof requestAnimationFrame timing
        const originalRAF = window.requestAnimationFrame;
        window.requestAnimationFrame = function(callback) {
          return originalRAF.call(this, function(timestamp) {
            // Add slight timing variation
            callback(timestamp + Math.random() * 0.5);
          });
        };
      })();

      console.log('🎭 Enhanced Antidetect script with Google evasion loaded for persona: ${persona.id}');
    `;
  }

  /**
   * Tạo Google-specific context options
   * @param {Object} persona - Persona object
   * @param {Object} proxy - Proxy object (optional)
   * @returns {Object} - Enhanced context options for Google services
   */
  createGoogleContextOptions(persona, proxy = null) {
    const baseOptions = this.createContextOptions(persona, proxy);

    // Add Google-specific enhancements
    return {
      ...baseOptions,
      javaScriptEnabled: true,
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      extraHTTPHeaders: {
        ...baseOptions.extraHTTPHeaders,
        'Sec-GPC': '1',
        'X-Forwarded-For': proxy ? proxy.ip : undefined,
        'CF-Connecting-IP': proxy ? proxy.ip : undefined,
        'X-Real-IP': proxy ? proxy.ip : undefined
      }
    };
  }
}

module.exports = AntidetectManager;
