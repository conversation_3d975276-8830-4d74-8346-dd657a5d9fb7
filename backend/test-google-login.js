const DatabaseManager = require('./src/database/manager');
const TikTokLoginAutomation = require('./src/automation/login');

// Mock WebSocket server for testing
class MockWebSocketServer {
  sendLog(level, message, accountId) {
    console.log(`[${level.toUpperCase()}] ${accountId ? `[${accountId}] ` : ''}${message}`);
  }
  
  sendAccountStatusUpdate(accountId, status) {
    console.log(`[STATUS UPDATE] ${accountId}: ${status}`);
  }
}

async function testGoogleLogin() {
  console.log('🧪 Testing Google Login Flow with Browser Close...\n');
  
  const dbManager = new DatabaseManager();
  const wsServer = new MockWebSocketServer();
  const loginAutomation = new TikTokLoginAutomation(wsServer, dbManager);
  
  try {
    // Initialize database
    await dbManager.initialize();
    
    // Get accounts
    const accounts = await dbManager.getAccounts();
    console.log(`📋 Found ${accounts.length} accounts`);
    
    if (accounts.length === 0) {
      console.log('❌ No accounts found. Please add accounts first.');
      return;
    }
    
    // Test with first account that uses Google login
    const googleAccount = accounts.find(acc => acc.useGoogleLogin);
    
    if (!googleAccount) {
      console.log('❌ No Google login account found. Please add a Google login account first.');
      return;
    }
    
    console.log(`\n🔍 Testing with account: ${googleAccount.username}`);
    console.log(`📧 Uses Google Login: ${googleAccount.useGoogleLogin}`);
    console.log(`📊 Current Status: ${googleAccount.status}`);
    
    // Test login
    console.log('\n🚀 Starting Google login test...');
    console.log('📝 Expected behavior:');
    console.log('   1. Browser opens to TikTok login page');
    console.log('   2. User manually completes Google OAuth (up to 5 minutes)');
    console.log('   3. System waits patiently with progress updates');
    console.log('   4. Browser automatically closes after success');
    console.log('   5. Status updates to "ready"');
    console.log('   6. Cookies are saved for future use');
    console.log('');
    console.log('⚠️  IMPORTANT: Do NOT close the browser manually!');
    console.log('⏰ You have 5 minutes to complete the login process');
    console.log('');
    
    const result = await loginAutomation.loginAccount(googleAccount.id);
    
    console.log(`\n✅ Login result: ${result}`);
    
    // Check final status
    const updatedAccount = await dbManager.getAccountById(googleAccount.id);
    console.log(`📊 Final Status: ${updatedAccount.status}`);
    
    // Check if browser is closed (should be null/undefined)
    const browserInstance = loginAutomation.getBrowser(googleAccount.id);
    console.log(`🌐 Browser instance exists: ${!!browserInstance}`);
    
    if (!browserInstance) {
      console.log('✅ Browser correctly closed after login');
    } else {
      console.log('❌ Browser still open - this is unexpected');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Cleanup any remaining browsers
    await loginAutomation.closeAllBrowsers();
    console.log('\n🧹 Cleanup completed');
  }
}

// Run test
if (require.main === module) {
  testGoogleLogin().catch(console.error);
}

module.exports = { testGoogleLogin };
