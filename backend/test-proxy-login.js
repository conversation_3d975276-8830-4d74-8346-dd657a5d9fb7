const DatabaseManager = require('./src/database/manager');
const TikTokLoginAutomation = require('./src/automation/login');

// Mock WebSocket server for testing
class MockWebSocketServer {
  sendLog(level, message, accountId) {
    const timestamp = new Date().toISOString().substr(11, 8);
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${accountId ? `[${accountId}] ` : ''}${message}`);
  }
  
  sendAccountStatusUpdate(accountId, status) {
    const timestamp = new Date().toISOString().substr(11, 8);
    console.log(`[${timestamp}] [STATUS] ${accountId}: ${status}`);
  }
}

async function testProxyLogin() {
  console.log('🧪 Testing Proxy Configuration in Login...\n');
  
  const dbManager = new DatabaseManager();
  const wsServer = new MockWebSocketServer();
  const loginAutomation = new TikTokLoginAutomation(wsServer, dbManager);
  
  try {
    // Initialize database
    await dbManager.initialize();
    
    // Get accounts
    const accounts = await dbManager.getAccounts();
    console.log(`📋 Found ${accounts.length} accounts\n`);
    
    if (accounts.length === 0) {
      console.log('❌ No accounts found. Please add accounts first.');
      return;
    }
    
    // Find account with proxy
    const accountWithProxy = accounts.find(acc => acc.proxy && acc.proxy.type !== 'No proxy');
    
    if (!accountWithProxy) {
      console.log('❌ No account with proxy found. Testing with first account...');
      
      // Use first account and show what would happen
      const testAccount = accounts[0];
      console.log(`\n🔍 Testing with account: ${testAccount.username}`);
      console.log(`📊 Current proxy: ${testAccount.proxy ? testAccount.proxy.type : 'None'}`);
      
      if (!testAccount.proxy || testAccount.proxy.type === 'No proxy') {
        console.log('ℹ️  This account will use direct connection (no proxy)');
      }
    } else {
      console.log(`\n🔍 Testing with account: ${accountWithProxy.username}`);
      console.log(`📊 Proxy type: ${accountWithProxy.proxy.type}`);
      console.log(`📊 Proxy host: ${accountWithProxy.proxy.host}:${accountWithProxy.proxy.port}`);
      if (accountWithProxy.proxy.username) {
        console.log(`📊 Proxy auth: ${accountWithProxy.proxy.username}:***`);
      }
      if (accountWithProxy.proxy.ip) {
        console.log(`📊 Expected IP: ${accountWithProxy.proxy.ip}`);
      }
    }
    
    const testAccount = accountWithProxy || accounts[0];
    
    console.log('\n🚀 Starting proxy test...');
    console.log('📝 What will be tested:');
    console.log('   1. Proxy configuration creation');
    console.log('   2. Browser launch with proxy');
    console.log('   3. IP verification');
    console.log('   4. Navigation to TikTok');
    console.log('');
    console.log('⚠️  Note: Browser will open for manual verification');
    console.log('⚠️  You can close the browser after seeing the IP verification');
    console.log('');
    
    // Test login (will open browser and verify proxy)
    const result = await loginAutomation.loginAccount(testAccount.id);
    
    console.log(`\n✅ Test completed with result: ${result}`);
    
    // Check final status
    const updatedAccount = await dbManager.getAccountById(testAccount.id);
    console.log(`📊 Final Status: ${updatedAccount.status}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    // Cleanup
    await loginAutomation.closeAllBrowsers();
    console.log('\n🧹 Cleanup completed');
  }
}

// Helper function to show proxy recommendations
function showProxyRecommendations() {
  console.log('\n💡 Proxy Configuration Recommendations:');
  console.log('');
  console.log('✅ SUPPORTED (Recommended):');
  console.log('   • HTTP proxy: http://host:port');
  console.log('   • HTTP with auth: *********************:port');
  console.log('   • HTTPS proxy: https://host:port');
  console.log('   • HTTPS with auth: **********************:port');
  console.log('   • SOCKS5 without auth: socks5://host:port');
  console.log('');
  console.log('⚠️  LIMITED SUPPORT:');
  console.log('   • SOCKS5 with auth: Not supported by browser');
  console.log('     → Use HTTP/HTTPS proxy instead');
  console.log('   • SSH proxy: Treated as HTTP proxy');
  console.log('');
  console.log('❌ NOT SUPPORTED:');
  console.log('   • SOCKS4 with password');
  console.log('   • Custom proxy protocols');
  console.log('');
}

// Run test
if (require.main === module) {
  showProxyRecommendations();
  testProxyLogin().catch(console.error);
}

module.exports = { testProxyLogin };
