import React, { useState } from 'react'
import AddAccountModal from './AddAccountModal'

function AccountTable({ accounts, onRefresh, wsClient }) {
  const [selectedAccounts, setSelectedAccounts] = useState(new Set())
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedAccounts(new Set(accounts.map(acc => acc.id)));
    } else {
      setSelectedAccounts(new Set());
    }
  };

  const handleSelectAccount = (accountId) => {
    const newSelected = new Set(selectedAccounts);
    if (newSelected.has(accountId)) {
      newSelected.delete(accountId);
    } else {
      newSelected.add(accountId);
    }
    setSelectedAccounts(newSelected);
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      'not_logged_in': { class: 'status-not-logged-in', text: '<PERSON><PERSON><PERSON> đăng nhập' },
      'logging_in': { class: 'status-logging-in', text: '<PERSON>ang đăng nhập' },
      'need_captcha': { class: 'status-error', text: 'Cần giải CAPTCHA' },
      'need_manual_login': { class: 'status-warning', text: 'Cần đăng nhập thủ công' },
      'ready': { class: 'status-ready', text: 'Sẵn sàng' },
      'running_interact': { class: 'status-running', text: 'Đang tương tác' },
      'running_follow': { class: 'status-running', text: 'Đang follow' },
      'resting': { class: 'status-secondary', text: 'Đang nghỉ' },
      'limit_reached': { class: 'status-secondary', text: 'Đã đạt giới hạn' },
      'stopped': { class: 'status-not-logged-in', text: 'Đã dừng' },
      'error': { class: 'status-error', text: 'Lỗi' }
    };

    const statusInfo = statusMap[status] || { class: 'status-not-logged-in', text: status };
    return (
      <span className={`status-badge ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    );
  };

  const getActionButton = (account) => {
    switch (account.status) {
      case 'not_logged_in':
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <button className="btn btn-primary btn-sm" onClick={() => handleLogin(account.id)}>
              Đăng nhập
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleDeleteAccount(account.id)}
              title="Xóa tài khoản"
            >
              🗑️
            </button>
          </div>
        );
      case 'need_captcha':
      case 'need_manual_login':
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <button className="btn btn-warning btn-sm" onClick={() => handleSolveCaptcha(account.id)}>
              {account.status === 'need_manual_login' ? 'Hoàn tất đăng nhập' : 'Giải quyết'}
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleDeleteAccount(account.id)}
              title="Xóa tài khoản"
            >
              🗑️
            </button>
          </div>
        );
      case 'need_manual_login':
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <button className="btn btn-primary btn-sm" onClick={() => handleLogin(account.id)}>
              Hoàn tất đăng nhập
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleDeleteAccount(account.id)}
              title="Xóa tài khoản"
            >
              🗑️
            </button>
          </div>
        );
      case 'error':
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <button className="btn btn-secondary btn-sm" onClick={() => handleRetry(account.id)}>
              Thử lại
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleDeleteAccount(account.id)}
              title="Xóa tài khoản"
            >
              🗑️
            </button>
          </div>
        );
      case 'logging_in':
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <button className="btn btn-secondary btn-sm" disabled>
              Đang xử lý...
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleDeleteAccount(account.id)}
              title="Xóa tài khoản"
            >
              🗑️
            </button>
          </div>
        );
      default:
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <button className="btn btn-secondary btn-sm" disabled>
              -
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleDeleteAccount(account.id)}
              title="Xóa tài khoản"
            >
              🗑️
            </button>
          </div>
        );
    }
  };

  const handleLogin = (accountId) => {
    if (wsClient) {
      wsClient.loginAccount(accountId);
    }
  };

  const handleSolveCaptcha = (accountId) => {
    // TODO: Implement CAPTCHA solving
    console.log('Solve CAPTCHA for account:', accountId);
  };

  const handleRetry = (accountId) => {
    if (wsClient) {
      wsClient.loginAccount(accountId);
    }
  };

  const handleBatchLogin = () => {
    if (wsClient && selectedAccounts.size > 0) {
      wsClient.loginAccountsBatch(Array.from(selectedAccounts));
    }
  };

  const handleAddAccount = async (accountData) => {
    try {
      wsClient.sendCommand('create_account', accountData)
      // Refresh accounts list after creation
      setTimeout(() => {
        onRefresh()
      }, 1000)
    } catch (error) {
      console.error('Error creating account:', error)
      throw error
    }
  }

  const handleDeleteAccount = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId)
    const accountName = account?.username || account?.id || 'Unknown'

    if (window.confirm(`Bạn có chắc chắn muốn xóa tài khoản "${accountName}"?\n\nHành động này không thể hoàn tác.`)) {
      try {
        wsClient.sendCommand('delete_account', { accountId })
        // Refresh accounts list after deletion
        setTimeout(() => {
          onRefresh()
        }, 500)
      } catch (error) {
        console.error('Error deleting account:', error)
        alert('Có lỗi xảy ra khi xóa tài khoản')
      }
    }
  }

  return (
    <div className="component-card">
      <div className="component-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Quản lý Tài khoản ({accounts.length})</span>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button className="btn btn-secondary btn-sm" onClick={onRefresh}>
              🔄 Làm mới
            </button>
            <button
              className="btn btn-success btn-sm"
              onClick={() => setIsAddModalOpen(true)}
            >
              ➕ Thêm tài khoản
            </button>
            <button
              className="btn btn-primary btn-sm"
              disabled={selectedAccounts.size === 0}
              onClick={handleBatchLogin}
            >
              Đăng nhập ({selectedAccounts.size})
            </button>
          </div>
        </div>
      </div>

      <div className="component-content" style={{ padding: 0 }}>
        {accounts.length === 0 ? (
          <div style={{ padding: '40px', textAlign: 'center', color: '#6b7280' }}>
            <p>Chưa có tài khoản nào được tải lên.</p>
            <p>Hãy sử dụng nút "Nạp Tài khoản" để tải danh sách tài khoản.</p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th style={{ width: '40px' }}>
                    <input
                      type="checkbox"
                      checked={selectedAccounts.size === accounts.length && accounts.length > 0}
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th>STT</th>
                  <th>Username</th>
                  <th>Proxy</th>
                  <th>Trạng thái</th>
                  <th>Follow hôm nay</th>
                  <th>Follow phiên này</th>
                  <th>Hoạt động cuối</th>
                  <th>Hành động</th>
                </tr>
              </thead>
              <tbody>
                {accounts.map((account, index) => (
                  <tr key={account.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedAccounts.has(account.id)}
                        onChange={() => handleSelectAccount(account.id)}
                      />
                    </td>
                    <td>{index + 1}</td>
                    <td>
                      <strong>{account.username}</strong>
                    </td>
                    <td>
                      {account.proxy ? (
                        <span style={{ fontSize: '12px', color: '#6b7280' }}>
                          {account.proxy.host}:{account.proxy.port}
                        </span>
                      ) : (
                        <span style={{ color: '#9ca3af' }}>Chưa gán</span>
                      )}
                    </td>
                    <td>
                      {getStatusBadge(account.status)}
                    </td>
                    <td>
                      <span style={{ fontWeight: '600' }}>
                        {account.stats?.followsToday || 0}
                      </span>
                    </td>
                    <td>
                      <span style={{ fontWeight: '600' }}>
                        {account.stats?.followsThisSession || 0}
                      </span>
                    </td>
                    <td>
                      {account.stats?.lastActivity ? (
                        <span style={{ fontSize: '12px', color: '#6b7280' }}>
                          {new Date(account.stats.lastActivity).toLocaleString('vi-VN')}
                        </span>
                      ) : (
                        <span style={{ color: '#9ca3af' }}>-</span>
                      )}
                    </td>
                    <td>
                      {getActionButton(account)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add Account Modal */}
      <AddAccountModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddAccount}
        wsClient={wsClient}
      />
    </div>
  )
}

export default AccountTable
